<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="7ae80ae2-21fc-48ff-aacf-745d562e90fa" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="32PDwZr1NGqDhiFNe9ItVz9isNR" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Application.hello.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "F:/javaweb",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "项目",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.0",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "vue.rearranger.settings.migration": "true",
    "应用程序.Test.executor": "Run",
    "应用程序.User.executor": "Run",
    "应用程序.hello.executor": "Run"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="hello" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="test1.hello" />
      <module name="javaweb" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="test1.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.hello" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-28b599e66164-intellij.indexing.shared.core-IU-242.23339.11" />
        <option value="bundled-js-predefined-d6986cc7102b-5c90d61e3bab-JavaScript-IU-242.23339.11" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="7ae80ae2-21fc-48ff-aacf-745d562e90fa" name="Changes" comment="" />
      <created>1757313552131</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1757313552131</updated>
      <workItem from="1757313553174" duration="472000" />
      <workItem from="1757314033923" duration="1186000" />
      <workItem from="1757315231355" duration="50000" />
      <workItem from="1757315287005" duration="3060000" />
      <workItem from="1757388335270" duration="982000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand>
      <path>
        <item name="javaweb" type="b2602c69:ProjectViewProjectNode" />
        <item name="dir{file://F:/javaweb}" type="462c0819:PsiDirectoryNode" />
      </path>
      <path>
        <item name="javaweb" type="b2602c69:ProjectViewProjectNode" />
        <item name="dir{file://F:/javaweb}" type="462c0819:PsiDirectoryNode" />
        <item name="dir{file://F:/javaweb/test1}" type="462c0819:PsiDirectoryNode" />
      </path>
      <path>
        <item name="javaweb" type="b2602c69:ProjectViewProjectNode" />
        <item name="dir{file://F:/javaweb}" type="462c0819:PsiDirectoryNode" />
        <item name="dir{file://F:/javaweb/test1}" type="462c0819:PsiDirectoryNode" />
        <item name="dir{file://F:/javaweb/test1/firstJava}" type="462c0819:PsiDirectoryNode" />
      </path>
    </expand>
    <select />
  </component>
</project>