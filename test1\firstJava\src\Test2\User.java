package Test2;

import java.util.Random;

public class User {
    public static void main(String[] args) {
        Vehicle car1 = new Vehicle(100);

        System.out.println("初始功率: " + car1.getPower());
        car1.setPower(150);
        System.out.println("设置后的功率: " + car1.getPower());

        System.out.println("初始速度: " + car1.getSpeed());
        car1.speedUp(30);
        System.out.println("加速30后的速度: " + car1.getSpeed());

        car1.speedDown(10);
        System.out.println("减速10后的速度: " + car1.getSpeed());

        System.out.println("\n=== 第二步：随机生成车辆演示 ===");

        Random random = new Random();
        int m = random.nextInt(10);
        System.out.println("随机生成的车辆数量: " + m);

        Vehicle[] cars = new Vehicle[m];
        for (int i = 0; i < m; i++) {
            int randomPower = 50 + random.nextInt(151);
            cars[i] = new Vehicle(randomPower);
            System.out.println("第" + (i + 1) + "辆车创建完成，功率: " + cars[i].getPower());
        }

        System.out.println("\n当前车辆总数量: " + Vehicle.getCarCount());

        if (m > 0) {
            System.out.println("\n=== 演示部分车辆的加速减速功能 ===");
            for (int i = 0; i < Math.min(3, m); i++) {
                System.out.println("第" + (i + 1) + "辆车:");
                System.out.println("  初始速度: " + cars[i].getSpeed());

                int speedIncrease = 10 + random.nextInt(41);
                cars[i].speedUp(speedIncrease);
                System.out.println("  加速" + speedIncrease + "后的速度: " + cars[i].getSpeed());

                int speedDecrease = random.nextInt(21);
                cars[i].speedDown(speedDecrease);
                System.out.println("  减速" + speedDecrease + "后的速度: " + cars[i].getSpeed());
                System.out.println();
            }
        }

        System.out.println("最终车辆总数量: " + Vehicle.getCarCount());
    }
}
