package Test;

import java.util.Random;
import java.util.Scanner;

public class Test {
    public static void main(String[] args){
        Random random = new Random();
        int oknum = random.nextInt(100);
        Scanner scanner = new Scanner(System.in);
        int count = 0;
        while (true){
            System.out.println("请输入要猜测的数：");
            int num = scanner.nextInt();
            count++;
            if (oknum > num){
                System.out.println("猜小了，请重新尝试");
            }else if(oknum < num){
                System.out.println("猜大了，请重新尝试");
            }else{
                System.out.println("恭喜你猜对了！");
                System.out.println("你一共猜了 " + count + " 次");
                String grade = getGrade(count);
                System.out.println("你的成绩等级是：" + grade);
                break;
            }
        }
    }

    public static String getGrade(int count) {
        if (count >= 1 && count < 7) {
            return "优秀";
        } else if (count >= 7 && count < 13) {
            return "良好";
        } else if (count >= 13 && count < 19) {
            return "中等";
        } else if (count >= 19 && count < 25) {
            return "及格";
        } else {
            return "不及格";
        }
    }
}
