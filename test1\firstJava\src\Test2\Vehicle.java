package Test2;

public class Vehicle {
    double speed;
    int power;
    static int carCount = 0;

    Vehicle(int p) {
        this.power = p;
        this.speed = 0.0;
        carCount++;
    }

    void speedUp(int s) {
        speed = speed + s;
    }

    void speedDown(int s) {
        speed = speed - s;
        if (speed < 0) {
            speed = 0;
        }
    }
    void setPower(int p) {
        this.power = p;
    }

    int getPower() {
        return this.power;
    }

    static int getCarCount() {
        return carCount;
    }

    double getSpeed() {
        return this.speed;
    }

}
